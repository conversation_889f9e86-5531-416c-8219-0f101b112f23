"""
Legacy Compatibility Wrapper for StockPal

This module provides backward compatibility for legacy stock_analyzer.py functionality
while encouraging migration to the new clean architecture.
"""

import warnings
from datetime import datetime
from typing import Any, Dict, List, Optional

from refactored_main import StockPalApplication


def analyze_stock(symbol: str, end_date: Optional[datetime] = None) -> Dict[str, Any]:
    """
    Legacy wrapper for stock analysis functionality.
    
    DEPRECATED: This function is deprecated. Use StockPalApplication.analyze_stock() instead.
    
    Args:
        symbol: Stock symbol to analyze
        end_date: End date for analysis (not used in new architecture)
        
    Returns:
        Analysis results dictionary
    """
    warnings.warn(
        "analyze_stock() is deprecated. Use StockPalApplication.analyze_stock() instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    app = StockPalApplication()
    
    try:
        # Use new architecture for analysis
        result = app.analyze_stock(symbol, days_back=365)
        
        if result is None:
            return {
                "symbol": symbol,
                "error": "Analysis failed",
                "recommendation": "HOLD",
                "analysis_date": datetime.now().strftime("%Y-%m-%d")
            }
            
        return result
        
    except Exception as e:
        return {
            "symbol": symbol,
            "error": str(e),
            "recommendation": "HOLD",
            "analysis_date": datetime.now().strftime("%Y-%m-%d")
        }


def main(analysis_symbols: Optional[List[str]] = None, analysis_date: Optional[str] = None) -> None:
    """
    Legacy wrapper for main analysis workflow.
    
    DEPRECATED: This function is deprecated. Use StockPalApplication.run_batch_analysis() instead.
    
    Args:
        analysis_symbols: List of symbols to analyze (None for all)
        analysis_date: Analysis date (not used in new architecture)
    """
    warnings.warn(
        "main() is deprecated. Use StockPalApplication.run_batch_analysis() instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    app = StockPalApplication()
    
    try:
        if analysis_symbols:
            print(f"Analyzing {len(analysis_symbols)} symbols using new architecture...")
            results = app.run_batch_analysis(symbols=analysis_symbols, export_results=True)
        else:
            print("Running batch analysis on all symbols using new architecture...")
            results = app.run_batch_analysis(test_mode=True, export_results=True)
            
        if results:
            print(f"✅ Analysis completed successfully!")
            print(f"   - Successful analyses: {results.get('successful_analyses', 0)}")
            print(f"   - Failed analyses: {results.get('failed_analyses', 0)}")
            if 'statistics' in results:
                stats = results['statistics']
                print(f"   - Success rate: {stats.get('success_rate', 0):.1f}%")
                if 'recommendations' in stats:
                    recs = stats['recommendations']
                    print(f"   - Buy recommendations: {recs.get('BUY', 0)}")
                    print(f"   - Hold recommendations: {recs.get('HOLD', 0)}")
                    print(f"   - Sell recommendations: {recs.get('SELL', 0)}")
        else:
            print("❌ Analysis failed")
            
    except Exception as e:
        print(f"❌ Analysis failed with error: {str(e)}")


# Legacy function aliases for backward compatibility
def save_stock_current_info(analysis: Dict[str, Any]) -> None:
    """Legacy function - functionality moved to ExportService."""
    warnings.warn(
        "save_stock_current_info() is deprecated. Use ExportService instead.",
        DeprecationWarning,
        stacklevel=2
    )
    print(f"Legacy save function called for {analysis.get('symbol', 'unknown')}")


def save_indicator_span(symbol: str, indicator_name: str, values: Dict[str, Any], 
                       prices: Optional[List[Any]] = None, **kwargs) -> None:
    """Legacy function - functionality moved to ExportService."""
    warnings.warn(
        "save_indicator_span() is deprecated. Use ExportService instead.",
        DeprecationWarning,
        stacklevel=2
    )
    print(f"Legacy indicator save function called for {symbol} - {indicator_name}")


def save_history_prices(symbol: str, days: int = 30) -> None:
    """Legacy function - functionality moved to ExportService."""
    warnings.warn(
        "save_history_prices() is deprecated. Use ExportService instead.",
        DeprecationWarning,
        stacklevel=2
    )
    print(f"Legacy price save function called for {symbol}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Legacy stock analyzer - DEPRECATED. Use refactored_main.py instead."
    )
    parser.add_argument("--symbols", nargs="+", help="Symbols to analyze")
    parser.add_argument("--date", help="Analysis date (ignored)")
    parser.add_argument("--deploy", action="store_true", help="Deploy mode (ignored)")
    
    args = parser.parse_args()
    
    print("⚠️  WARNING: This is a legacy compatibility wrapper.")
    print("   Please use 'python refactored_main.py' for the new architecture.")
    print("   This wrapper will be removed in a future version.\n")
    
    main(analysis_symbols=args.symbols, analysis_date=args.date)
